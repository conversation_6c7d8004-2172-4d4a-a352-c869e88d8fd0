import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import VariableFilters from "./VariableFilters";
import {
  FaEdit,
  FaTrash,
  FaPlus,
  FaArrowLeft,
  FaCode,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import VariableForm from "./VariableForm";

const VariableIndex = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [filterOptions, setFilterOptions] = useState({});
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedVariable, setSelectedVariable] = useState(null);
  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
  } = usePagination(1, 10);

  const { deleteMutation } = useApi();

  const {
    data: variablesData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/email-templates/variables", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    category: filterOptions.category || "",
    data_type: filterOptions.data_type || "",
    is_required: filterOptions.is_required || "",
  });

  // Handle nested API response structure: {status: true, data: {data: [...], meta: {...}}}
  const variables = variablesData?.data?.data || [];
  const paginationInfo = {
    currentPage: variablesData?.data?.current_page || 1,
    perPage: variablesData?.data?.per_page || itemsPerPage,
    totalItems: variablesData?.data?.total_items || 0,
    totalPages: variablesData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleCreateClick = () => {
    setSelectedVariable(null);
    setShowCreateModal(true);
  };

  const handleEditClick = (variable) => {
    setSelectedVariable(variable);
    setShowEditModal(true);
  };

  const handleDeleteClick = (variable) => {
    setSelectedVariable(variable);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteMutation.mutateAsync({
        endpoint: `admin/email-templates/variables/${selectedVariable.id}`,
      });
      toast.success(t("emailTemplate.variable.deleteSuccess"));
      refetch();
      setShowDeleteModal(false);
      setSelectedVariable(null);
    } catch (error) {
      const errorMessage = error.response?.data?.message || t("emailTemplate.variable.deleteError");
      toast.error(errorMessage);
    }
  };

  const handleFormSuccess = () => {
    refetch();
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedVariable(null);
  };





  const columns = [
    {
      header: t("emailTemplate.variable.key"),
      accessor: "key",
      className: "min-w-[200px]",
      render: (variable) => (
        <div className="flex items-center space-x-3">
          <FaCode className="w-4 h-4 text-blue-500 flex-shrink-0" />
          <div className="min-w-0 flex-1">
            <div className="font-mono text-sm text-blue-600 truncate">{variable.key}</div>
            <div className="text-xs text-gray-500 truncate">{variable.name}</div>
          </div>
        </div>
      ),
    },
    {
      header: t("emailTemplate.variable.description"),
      accessor: "description",
      className: "min-w-[150px] max-w-[300px]",
      render: (variable) => (
        <span className="text-sm text-gray-600 block truncate" title={variable.description}>
          {variable.description || "—"}
        </span>
      ),
    },
    {
      header: t("emailTemplate.variable.categoryLabel"),
      accessor: "category",
      className: "min-w-[120px]",
      render: (variable) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 whitespace-nowrap">
          {t(`emailTemplate.variable.category.${variable.category}`)}
        </span>
      ),
    },
    {
      header: t("emailTemplate.variable.dataTypeLabel"),
      accessor: "data_type",
      className: "min-w-[100px]",
      render: (variable) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 whitespace-nowrap">
          {t(`emailTemplate.variable.dataType.${variable.data_type}`)}
        </span>
      ),
    },
    {
      header: t("emailTemplate.variable.required"),
      accessor: "is_required",
      className: "min-w-[100px]",
      render: (variable) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium whitespace-nowrap ${
            variable.is_required
              ? "bg-red-100 text-red-800"
              : "bg-green-100 text-green-800"
          }`}
        >
          {variable.is_required ? t("commonOptions.required") : t("commonOptions.optional")}
        </span>
      ),
    },
    {
      header: t("emailTemplate.variable.example"),
      accessor: "example_value",
      className: "min-w-[120px] max-w-[200px]",
      render: (variable) => (
        <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded block truncate" title={variable.example_value}>
          {variable.example_value || "—"}
        </span>
      ),
    },
    {
      header: t("common.actions"),
      accessor: "actions",
      className: "min-w-[100px] w-[100px]",
      render: (variable) => (
        <div className="flex items-center justify-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditClick(variable)}
            className="text-green-600 hover:text-green-800 p-1"
            title={t("common.edit")}
          >
            <FaEdit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteClick(variable)}
            className="text-red-600 hover:text-red-800 p-1"
            title={t("common.delete")}
          >
            <FaTrash className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner size={40} />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{t("emailTemplate.variable.loadError")}</p>
        <Button onClick={() => refetch()} className="mt-4">
          {t("common.retry")}
        </Button>
      </div>
    );
  }

  return (
    <div className="mx-auto relative p-5">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <div>
        <Card
          title={t("emailTemplate.variable.title")}
          icon={<FaCode className="text-indigo-600 me-1" />}
          action={
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Button
                onClick={() => navigate("/email-templates")}
                variant="outline"
                className="flex items-center justify-center sm:justify-start space-x-2 w-full sm:w-auto"
                size="sm"
              >
                <FaArrowLeft className="w-4 h-4" />
                <span className="hidden sm:inline">{t("common.back")}</span>
                <span className="sm:hidden">{t("common.back")}</span>
              </Button>
              <Button
                onClick={handleCreateClick}
                className="flex items-center justify-center sm:justify-start space-x-2 w-full sm:w-auto"
                size="sm"
              >
                <FaPlus className="w-4 h-4" />
                <span className="hidden sm:inline">{t("emailTemplate.variable.createNew")}</span>
                <span className="sm:hidden">{t("emailTemplate.variable.add")}</span>
              </Button>
            </div>
          }
        >
          <VariableFilters
            onFilterChange={handleFilterChange}
            filterOptions={filterOptions}
          />

          <div className="overflow-x-auto">
            <Table
              columns={columns}
              data={variables}
              emptyMessage={t("emailTemplate.variable.noVariables")}
            />
          </div>

          <PaginationInfo
            currentPage={paginationInfo.currentPage}
            totalPages={paginationInfo.totalPages}
            totalItems={paginationInfo.totalItems}
            itemsPerPage={paginationInfo.perPage}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </Card>
      </div>

      {/* Create Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title={t("emailTemplate.variable.createTitle")}
        size="lg"
      >
        <VariableForm
          onSuccess={handleFormSuccess}
          onCancel={() => setShowCreateModal(false)}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title={t("emailTemplate.variable.editTitle")}
        size="lg"
      >
        <VariableForm
          variable={selectedVariable}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowEditModal(false)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t("emailTemplate.variable.deleteConfirmTitle")}
        size="sm"
      >
        <div className="p-6">
          <p className="text-gray-600 mb-6">
            {t("emailTemplate.variable.deleteConfirmMessage", {
              key: selectedVariable?.key,
            })}
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              {t("common.cancel")}
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteConfirm}
              loading={deleteMutation.isPending}
            >
              {t("common.delete")}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default VariableIndex;
