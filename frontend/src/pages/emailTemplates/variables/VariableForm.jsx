import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import {
  FormInput,
  FormTextarea,
  FormSelect,
  FormRadioGroup,
} from "@/components/ui/form";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useApi } from "@/hooks/useApi";

const VariableForm = ({ variable, onSuccess, onCancel }) => {
  const { t } = useTranslation();
  const { postMutation, putMutation } = useApi();
  const isEditing = !!variable;

  const initialValues = variable
    ? {
        name: variable.name || "",
        key: variable.key || "",
        description: variable.description || "",
        data_type: variable.data_type || "string",
        category: variable.category || "user",
        is_required: variable.is_required ? "1" : "0",
        default_value: variable.default_value || "",
        example_value: variable.example_value || "",
        validation_rules: {
          max_length: variable.validation_rules?.max_length || "",
          pattern: variable.validation_rules?.pattern || "",
        },
      }
    : {
        name: "",
        key: "",
        description: "",
        data_type: "string",
        category: "user",
        is_required: "0",
        default_value: "",
        example_value: "",
        validation_rules: {
          max_length: "",
          pattern: "",
        },
      };

  const validationSchema = Yup.object({
    name: Yup.string()
      .required(t("emailTemplate.variable.validation.nameRequired"))
      .min(2, t("emailTemplate.variable.validation.nameMin"))
      .max(100, t("emailTemplate.variable.validation.nameMax")),
    key: Yup.string()
      .required(t("emailTemplate.variable.validation.keyRequired"))
      .matches(
        /^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)*$/,
        t("emailTemplate.variable.validation.keyFormat")
      )
      .max(100, t("emailTemplate.variable.validation.keyMax")),
    description: Yup.string()
      .max(500, t("emailTemplate.variable.validation.descriptionMax")),
    data_type: Yup.string()
      .required(t("emailTemplate.variable.validation.dataTypeRequired")),
    category: Yup.string()
      .required(t("emailTemplate.variable.validation.categoryRequired")),
    example_value: Yup.string()
      .max(255, t("emailTemplate.variable.validation.exampleMax")),
    default_value: Yup.string()
      .max(255, t("emailTemplate.variable.validation.defaultMax")),
  });

  const categoryOptions = [
    { value: "user", label: t("emailTemplate.variable.category.user") },
    { value: "order", label: t("emailTemplate.variable.category.order") },
    { value: "vendor", label: t("emailTemplate.variable.category.vendor") },
    { value: "site", label: t("emailTemplate.variable.category.site") },
    { value: "auth", label: t("emailTemplate.variable.category.auth") },
  ];

  const dataTypeOptions = [
    { value: "string", label: t("emailTemplate.variable.dataType.string") },
    { value: "number", label: t("emailTemplate.variable.dataType.number") },
    { value: "boolean", label: t("emailTemplate.variable.dataType.boolean") },
    { value: "array", label: t("emailTemplate.variable.dataType.array") },
    { value: "object", label: t("emailTemplate.variable.dataType.object") },
  ];

  const requiredOptions = [
    { value: "1", label: t("commonOptions.required") },
    { value: "0", label: t("commonOptions.optional") },
  ];

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      const formData = {
        ...values,
        is_required: values.is_required === "1",
        validation_rules: {
          ...(values.validation_rules.max_length && {
            max_length: parseInt(values.validation_rules.max_length),
          }),
          ...(values.validation_rules.pattern && {
            pattern: values.validation_rules.pattern,
          }),
        },
      };

      // Remove empty validation rules
      if (Object.keys(formData.validation_rules).length === 0) {
        delete formData.validation_rules;
      }

      if (isEditing) {
        await putMutation.mutateAsync({
          endpoint: `admin/email-templates/variables/${variable.id}`,
          data: formData,
        });
        toast.success(t("emailTemplate.variable.updateSuccess"));
      } else {
        await postMutation.mutateAsync({
          endpoint: "admin/email-templates/variables",
          data: formData,
        });
        toast.success(t("emailTemplate.variable.createSuccess"));
      }

      onSuccess();
    } catch (error) {
      const errorMessage = error.response?.data?.message || 
        (isEditing 
          ? t("emailTemplate.variable.updateError") 
          : t("emailTemplate.variable.createError"));
      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const generateKeyFromName = (name, setFieldValue) => {
    const key = name
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, "")
      .replace(/\s+/g, "_")
      .replace(/^_+|_+$/g, "");
    setFieldValue("key", key);
  };

  const isLoading = postMutation.isLoading || putMutation.isLoading;

  return (
    <div className="p-6">
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, setFieldValue, values }) => (
          <Form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                name="name"
                label={t("emailTemplate.variable.name")}
                placeholder={t("emailTemplate.variable.namePlaceholder")}
                onChange={(e) => {
                  setFieldValue("name", e.target.value);
                  if (!isEditing) {
                    generateKeyFromName(e.target.value, setFieldValue);
                  }
                }}
                required
              />
              <FormInput
                name="key"
                label={t("emailTemplate.variable.key")}
                placeholder={t("emailTemplate.variable.keyPlaceholder")}
                help={t("emailTemplate.variable.keyHelp")}
                disabled={isEditing}
                required
              />
            </div>

            <FormTextarea
              name="description"
              label={t("emailTemplate.variable.description")}
              placeholder={t("emailTemplate.variable.descriptionPlaceholder")}
              rows={3}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormSelect
                name="category"
                label={t("emailTemplate.variable.categoryLabel")}
                options={categoryOptions}
                required
              />
              <FormSelect
                name="data_type"
                label={t("emailTemplate.variable.dataTypeLabel")}
                options={dataTypeOptions}
                required
              />
            </div>

            <FormRadioGroup
              name="is_required"
              label={t("emailTemplate.variable.required")}
              options={requiredOptions}
              required
              className="mb-4"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                name="example_value"
                label={t("emailTemplate.variable.exampleValue")}
                placeholder={t("emailTemplate.variable.examplePlaceholder")}
                help={t("emailTemplate.variable.exampleHelp")}
              />
              <FormInput
                name="default_value"
                label={t("emailTemplate.variable.defaultValue")}
                placeholder={t("emailTemplate.variable.defaultPlaceholder")}
                help={t("emailTemplate.variable.defaultHelp")}
              />
            </div>

            {/* Validation Rules */}
            <div className="border-t pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">
                {t("emailTemplate.variable.validationRules")}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  name="validation_rules.max_length"
                  type="number"
                  label={t("emailTemplate.variable.maxLength")}
                  placeholder="255"
                  help={t("emailTemplate.variable.maxLengthHelp")}
                />
                <FormInput
                  name="validation_rules.pattern"
                  label={t("emailTemplate.variable.pattern")}
                  placeholder="^[a-zA-Z0-9]+$"
                  help={t("emailTemplate.variable.patternHelp")}
                />
              </div>
            </div>

            {/* Preview */}
            {values.key && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  {t("emailTemplate.variable.preview")}
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">
                      {t("emailTemplate.variable.usage")}:
                    </span>
                    <code className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                      {`{{${values.key}}}`}
                    </code>
                  </div>
                  {values.example_value && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">
                        {t("emailTemplate.variable.exampleOutput")}:
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {values.example_value}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
              >
                {t("common.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || isLoading}
                className="flex items-center space-x-2"
              >
                {isLoading && <LoadingSpinner size={16} />}
                <span>
                  {isEditing ? t("common.update") : t("common.create")}
                </span>
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default VariableForm;
