import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import CategoryFilters from "./CategoryFilters";
import {
  FaEdit,
  FaTrash,
  FaPlus,
  FaArrowLeft,
  FaFolder,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import CategoryForm from "./CategoryForm";

const CategoryIndex = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [filterOptions, setFilterOptions] = useState({});
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
  } = usePagination(1, 10);

  const { deleteMutation } = useApi();

  const {
    data: categoriesData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/email-templates/categories", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    is_active: filterOptions.is_active || "",
  });

  // Handle nested API response structure: {status: true, data: {data: [...], meta: {...}}}
  const categories = categoriesData?.data?.data || [];
  const paginationInfo = {
    currentPage: categoriesData?.data?.current_page || 1,
    perPage: categoriesData?.data?.per_page || itemsPerPage,
    totalItems: categoriesData?.data?.total_items || 0,
    totalPages: categoriesData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleCreateClick = () => {
    setSelectedCategory(null);
    setShowCreateModal(true);
  };

  const handleEditClick = (category) => {
    setSelectedCategory(category);
    setShowEditModal(true);
  };

  const handleDeleteClick = (category) => {
    setSelectedCategory(category);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteMutation.mutateAsync({
        endpoint: `admin/email-templates/categories/${selectedCategory.slug}`,
      });
      toast.success(t("emailTemplate.categoryManagement.deleteSuccess"));
      refetch();
      setShowDeleteModal(false);
      setSelectedCategory(null);
    } catch (error) {
      const errorMessage = error.response?.data?.message || t("emailTemplate.categoryManagement.deleteError");
      toast.error(errorMessage);
    }
  };

  const handleFormSuccess = () => {
    refetch();
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedCategory(null);
  };

  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.categoryName"),
      accessor: "name",
      render: (category) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <FaFolder className="w-5 h-5 text-blue-500" />
          </div>
          <div>
            <div className="font-medium text-gray-900">{category.name}</div>
            <div className="text-sm text-gray-500">{category.slug}</div>
          </div>
        </div>
      ),
    },
    {
      header: t("commonTableLabel.description"),
      accessor: "description",
      render: (category) => (
        <span className="text-sm text-gray-600">
          {category.description || "—"}
        </span>
      ),
    },
    {
      header: t("commonTableLabel.templatesCount"),
      accessor: "templates_count",
      render: (category) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {category.templates_count || 0}
        </span>
      ),
    },
    {
      header: t("commonTableLabel.status"),
      accessor: "is_active",
      render: (category) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            category.is_active
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {category.is_active ? t("commonOptions.status.active") : t("commonOptions.status.inactive")}
        </span>
      ),
    },
    {
      header: t("commonTableLabel.actions"),
      accessor: "actions",
      render: (category) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditClick(category)}
            title={t("emailTemplate.categoryManagement.edit")}
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(category)}
            disabled={category.templates_count > 0}
            title={category.templates_count > 0 ? t("emailTemplate.categoryManagement.deleteWarning", {count: category.templates_count}) : t("emailTemplate.categoryManagement.delete")}
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner size={40} />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{t("emailTemplate.categoryManagement.loadError")}</p>
        <Button onClick={() => refetch()} className="mt-4">
          {t("common.retry")}
        </Button>
      </div>
    );
  }

  return (
    <div className="mx-auto relative p-5">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <div>
        <Card
          title={t("emailTemplate.categoryManagement.title")}
          icon={<FaFolder className="text-indigo-600 me-1" />}
          action={
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Button
                onClick={() => navigate("/email-templates")}
                variant="outline"
                className="flex items-center justify-center sm:justify-start space-x-2 w-full sm:w-auto"
                size="sm"
              >
                <FaArrowLeft className="w-4 h-4" />
                <span className="hidden sm:inline">{t("common.back")}</span>
                <span className="sm:hidden">{t("common.back")}</span>
              </Button>
              <Button
                onClick={handleCreateClick}
                className="flex items-center justify-center sm:justify-start space-x-2 w-full sm:w-auto"
                size="sm"
              >
                <FaPlus className="w-4 h-4" />
                <span className="hidden sm:inline">{t("emailTemplate.categoryManagement.createNew")}</span>
                <span className="sm:hidden">{t("emailTemplate.categoryManagement.add")}</span>
              </Button>
            </div>
          }
        >
          <CategoryFilters
            onFilterChange={handleFilterChange}
            filterOptions={filterOptions}
          />

          <Table
            columns={columns}
            data={categories}
            emptyMessage={t("emailTemplate.categoryManagement.noCategories")}
          />

          <PaginationInfo
            currentPage={paginationInfo.currentPage}
            totalPages={paginationInfo.totalPages}
            totalItems={paginationInfo.totalItems}
            itemsPerPage={paginationInfo.perPage}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </Card>
      </div>

      {/* Create Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title={t("emailTemplate.categoryManagement.createTitle")}
        size="lg"
      >
        <CategoryForm
          onSuccess={handleFormSuccess}
          onCancel={() => setShowCreateModal(false)}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title={t("emailTemplate.categoryManagement.editTitle")}
        size="lg"
      >
        <CategoryForm
          category={selectedCategory}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowEditModal(false)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t("emailTemplate.categoryManagement.deleteConfirmTitle")}
      >
        <div className="p-6">
          <p className="text-gray-600 mb-4">
            {t("emailTemplate.categoryManagement.deleteConfirmMessage", {
              name: selectedCategory?.name,
            })}
          </p>
          {selectedCategory?.templates_count > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
              <p className="text-yellow-800 text-sm">
                {t("emailTemplate.categoryManagement.deleteWarning", {
                  count: selectedCategory.templates_count,
                })}
              </p>
            </div>
          )}
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              {t("common.cancel")}
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteConfirm}
              disabled={deleteMutation.isLoading || selectedCategory?.templates_count > 0}
            >
              {deleteMutation.isLoading ? (
                <LoadingSpinner size={16} />
              ) : (
                t("common.delete")
              )}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CategoryIndex;
