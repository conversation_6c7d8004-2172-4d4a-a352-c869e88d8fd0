import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import SearchInput from "@/components/ui/SearchInput";
import SingleSelectDropdown from "@/components/ProductWizard/components/SingleSelectDropdown";
import Button from "@/components/ui/Button";
import { FaFilter, FaTimes, FaChevronDown, FaSearch, FaSpinner } from "react-icons/fa";
import { fetchData } from "@/hooks/useApi";

const ProductFilters = ({ onChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: '',
    consolidated_status: '',
    vendor_id: '',
    category_id: '',
    brand_id: '',
    stock_status: ''
  });

  const [selectedVendorName, setSelectedVendorName] = useState('');

  // Fetch categories for filter dropdown using the correct endpoint
  const { data: categoriesData } = fetchData("general/categories/active-list", { pagination: false });
  const categories = categoriesData?.data || [];

  // Fetch brands for filter dropdown using the correct endpoint
  const { data: brandsData } = fetchData("general/brands/active-list", { pagination: false });
  const brands = brandsData?.data || [];

  // Fetch vendors for filter dropdown using the correct endpoint
  const { data: vendorsData, isLoading: vendorsLoading, error: vendorsError } = fetchData("general/vendors/active-list", { pagination: false });
  const vendors = vendorsData?.data || [];

  // Debug vendors data
  useEffect(() => {
    console.log('Main vendors fetch:');
    console.log('Vendors Data:', vendorsData);
    console.log('Vendors Loading:', vendorsLoading);
    console.log('Vendors Error:', vendorsError);
    console.log('Vendors Array:', vendors);
  }, [vendorsData, vendorsLoading, vendorsError, vendors]);

  // Consolidated status options that combine status, is_active, and is_approved logic
  const consolidatedStatusOptions = [
    { label: "All Products", value: "" },
    { label: "Live & Active", value: "live_active", description: "Active status, Active, Approved" },
    { label: "Draft", value: "draft", description: "Draft status" },
    { label: "Pending Review", value: "pending", description: "Pending status" },
    { label: "Submitted", value: "submitted", description: "Submitted status" },
    { label: "Active (Not Approved)", value: "active_not_approved", description: "Active status but not approved" },
    { label: "Inactive", value: "inactive", description: "Inactive status" },
    { label: "Approved (Not Active)", value: "approved_not_active", description: "Approved but not active" },
    { label: "Not Approved", value: "not_approved", description: "Not approved products" },
  ];

  const stockStatusOptions = [
    { label: "All Stock", value: "" },
    { label: "In Stock", value: "in_stock" },
    { label: "Low Stock", value: "low_stock" },
    { label: "Out of Stock", value: "out_of_stock" },
  ];

  const categoryOptions = [
    { label: "All Categories", value: "" },
    ...categories.map(category => ({
      label: category.name_en || category.name,
      value: category.id
    }))
  ];

  const brandOptions = [
    { label: "All Brands", value: "" },
    ...brands.map(brand => ({
      label: brand.name_en || brand.name,
      value: brand.id
    }))
  ];

  const vendorOptions = [
    { label: "All Vendors", value: "" },
    ...vendors.map(vendor => ({
      label: vendor.vendor_display_name_en || vendor.name_tl_en || vendor.spoc_name || 'Unknown Vendor',
      value: vendor.id
    }))
  ];

  // Convert consolidated status to individual backend filters
  const getBackendFiltersFromConsolidatedStatus = (consolidatedStatus) => {
    switch (consolidatedStatus) {
      case 'live_active':
        return { status: 'active', is_active: '1', is_approved: '1' };
      case 'draft':
        return { status: 'draft', is_active: '', is_approved: '' };
      case 'pending':
        return { status: 'pending', is_active: '', is_approved: '' };
      case 'submitted':
        return { status: 'submitted', is_active: '', is_approved: '' };
      case 'active_not_approved':
        return { status: 'active', is_active: '1', is_approved: '0' };
      case 'inactive':
        return { status: 'inactive', is_active: '0', is_approved: '' };
      case 'approved_not_active':
        return { status: '', is_active: '0', is_approved: '1' };
      case 'not_approved':
        return { status: '', is_active: '', is_approved: '0' };
      default:
        return { status: '', is_active: '', is_approved: '' };
    }
  };

  const handleFilterChange = (key, value) => {
    let newFilters = { ...filters, [key]: value };

    // If consolidated_status is being changed, convert it to backend filters
    if (key === 'consolidated_status') {
      const backendFilters = getBackendFiltersFromConsolidatedStatus(value);
      newFilters = { ...newFilters, ...backendFilters };
    }

    setFilters(newFilters);
    onChange(newFilters);
  };

  // Special handler for vendor selection to store vendor name for display
  const handleVendorChange = (vendorId, vendorData) => {
    if (vendorData) {
      const vendorName = vendorData.vendor_display_name_en || vendorData.name_tl_en || vendorData.spoc_name || 'Selected Vendor';
      setSelectedVendorName(vendorName);
    } else {
      setSelectedVendorName('');
    }
    handleFilterChange('vendor_id', vendorId);
  };

  const handleSearchChange = (value) => {
    handleFilterChange('search', value);
  };

  const clearFilters = () => {
    const clearedFilters = {
      search: '',
      consolidated_status: '',
      status: '',
      is_active: '',
      is_approved: '',
      vendor_id: '',
      category_id: '',
      brand_id: '',
      stock_status: ''
    };
    setFilters(clearedFilters);
    setSelectedVendorName('');
    onChange(clearedFilters);
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 mb-6">
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search Input */}
        <div className="flex-1">
          <SearchInput
            placeholder={t("products.searchPlaceholder") || "Search products..."}
            value={filters.search}
            onChange={handleSearchChange}
            className="w-full"
          />
        </div>

        {/* Filter Dropdowns */}
        <div className="flex flex-wrap gap-3">
          <SingleSelectDropdown
            label="Vendor"
            options={vendorOptions}
            value={filters.vendor_id}
            onChange={(value) => {
              const selectedVendor = vendors.find(v => String(v.id) === String(value));
              handleVendorChange(value, selectedVendor);
            }}
            className="min-w-[180px]"
            placeholder="All Vendors"
          />

          <SingleSelectDropdown
            label="Status"
            options={consolidatedStatusOptions}
            value={filters.consolidated_status}
            onChange={(value) => handleFilterChange('consolidated_status', value)}
            className="min-w-[160px]"
            placeholder="All Products"
          />

          <SingleSelectDropdown
            label="Category"
            options={categoryOptions}
            value={filters.category_id}
            onChange={(value) => handleFilterChange('category_id', value)}
            className="min-w-[140px]"
            placeholder="All Categories"
          />

          <SingleSelectDropdown
            label="Brand"
            options={brandOptions}
            value={filters.brand_id}
            onChange={(value) => handleFilterChange('brand_id', value)}
            className="min-w-[120px]"
            placeholder="All Brands"
          />

          <SingleSelectDropdown
            label="Stock Status"
            options={stockStatusOptions}
            value={filters.stock_status}
            onChange={(value) => handleFilterChange('stock_status', value)}
            className="min-w-[130px]"
            placeholder="All Stock"
          />

          {/* Clear Filters Button */}
          {hasActiveFilters && (
            <Button
              variant="outline"
              onClick={clearFilters}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 px-4 py-2 h-[42px]"
            >
              <FaTimes className="w-3 h-3" />
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-gray-600 font-medium">Active filters:</span>
            
            {filters.search && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Search: "{filters.search}"
                <button
                  onClick={() => handleFilterChange('search', '')}
                  className="ml-1 hover:text-blue-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}

            {filters.vendor_id && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">
                Vendor: {selectedVendorName || 'Selected'}
                <button
                  onClick={() => {
                    handleFilterChange('vendor_id', '');
                    setSelectedVendorName('');
                  }}
                  className="ml-1 hover:text-indigo-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}

            {filters.consolidated_status && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                Status: {consolidatedStatusOptions.find(opt => opt.value === filters.consolidated_status)?.label}
                <button
                  onClick={() => handleFilterChange('consolidated_status', '')}
                  className="ml-1 hover:text-green-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}

            {filters.category_id && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                Category: {categoryOptions.find(opt => opt.value === filters.category_id)?.label}
                <button
                  onClick={() => handleFilterChange('category_id', '')}
                  className="ml-1 hover:text-purple-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}

            {filters.brand_id && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                Brand: {brandOptions.find(opt => opt.value === filters.brand_id)?.label}
                <button
                  onClick={() => handleFilterChange('brand_id', '')}
                  className="ml-1 hover:text-orange-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}

            {filters.stock_status && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                Stock: {stockStatusOptions.find(opt => opt.value === filters.stock_status)?.label}
                <button
                  onClick={() => handleFilterChange('stock_status', '')}
                  className="ml-1 hover:text-red-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductFilters;
