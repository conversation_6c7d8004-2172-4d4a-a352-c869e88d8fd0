{"app": {"title": "Vitamins.ae", "language": "Language"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "haveAccount": "Already have an account?", "signUp": "Sign Up", "signIn": "Sign In", "name": "Full Name", "welcomeMessage": "Welcome to the Future of Vitamins in UAE", "welcomeDescription": "Discover a world of premium products and exceptional shopping experiences.", "rememberMe": "Remember me"}, "dashboard": {"welcome": "Welcome to your Dashboard", "orders": "Orders", "preorders": "Preorders", "earnings": "Earnings", "customers": "Customers", "products": "Products", "settings": "Settings", "logout": "Logout", "profile": "Profile Settings", "stats": {"totalSales": "Total Sales", "totalOrders": "Total Orders", "averageOrder": "Average Order", "conversionRate": "Conversion Rate", "totalCustomer": "Total Customer", "totalVendor": "Total Vendor", "totalProducts": "Total Products", "promotions": "Promotions", "topProduct": "Top Product", "topVendor": "Top Vendor", "totalCategory": "Total Category", "totalBrand": "Total Brand", "topCategory": "Top Category", "inHouseProducts": "In-House Products", "platformRevenue": "Platform Revenue", "orderCount": "Order Count", "taskPriority": "Task Priority", "pendingTasks": "Pending Tasks"}, "details": {"topCustomer": "Top Customer", "visitors": "Visitors", "buyers": "Buyers", "approvedVendor": "Approved Vendor", "topVendorList": "Top Vendor", "pendingVendor": "Pending Vendor", "inHouse": "In-house", "vendorProducts": "Vendor Products", "productsOnPromotion": "Products on Promotion", "sold": "Sold", "bySalesRating": "By Sales/Rating", "byPeriod": "Monthly Sale", "saleAmount": "Sale Amount", "bySource": "By In-house/Vendor", "byCategory": "By Top Category", "byBrand": "By Top Brand", "byPlatform": "By Entire Platform", "fromVendor": "From Vendor", "placed": "Placed", "confirmed": "Confirmed", "processed": "Processed", "pending": "Pending", "taskList": "Task List", "orderStatus": "Order Status", "advertisingRevenue": "Advertising Revenue", "shippingExpense": "Shipping Expense", "tasks": "Tasks", "remaining": "% remaining", "noTasksInProgress": "No tasks in progress", "completed": "Completed", "inquiries": "inquiries", "products": "products", "items": "items"}, "charts": {"timeSales": "Time-based Sales", "categorySales": "Category Wise Sales", "brandSales": "Brand Wise Sales"}}, "navigation": {"dashboard": "Dashboard", "orders": "Orders", "allorders": "All Orders", "pendingorders": "Pending Orders", "completedorders": "Completed Orders", "cancelledorders": "Cancelled Orders", "products": "Products", "allproducts": "All Products", "categories": "Categories", "category": "Category", "brand": "Brand", "productreviews": "Product Reviews", "catalog": "Catalog", "digitalproducts": "Digital Products", "physicalproducts": "Physical Products", "customers": "Customers", "salesandorder": "Sales and Order", "partnervendors": "Partner Vendors", "reports": "Reports", "salesreports": "Sales Reports", "inventoryreports": "Inventory Reports", "customerreports": "Customer Reports", "vendorreports": "Vendor Reports", "blogmanagement": "Blog", "allposts": "All Posts", "comments": "Comments", "emailtemplates": "Email Templates", "alltemplates": "All Templates", "variables": "Variables", "marketing": "Marketing", "campaigns": "Campaigns", "promotions": "Promotions", "coupons": "Coupons", "support": "Support", "homepagesetup": "Homepage Setup", "staffsadminmanager": "Staffs/Admin Manager", "users": "Users", "overview": "Overview", "usermanagement": "User", "delivery": "Delivery", "loyaltyandrewardpoints": "Loyalty and Reward Points", "demoform": "Demo Form", "demolist": "Demo List", "settings": "Settings", "roles": "Roles", "permissions": "Permissions", "staticpages": "Pages", "configurations": "Configurations", "banners": "Banners", "banneritem": "Banner Item", "class": "Class", "blog": "Blog", "attribute": "Attribute", "vendormanagement": "<PERSON><PERSON><PERSON>", "vendor": "Vendor EOI", "vendor_list": "Vendor List", "addproduct": "Add Products", "setting": "Settings", "dropdownmenu": "Dropdown", "banner": "Banner", "topic": "Topics", "reason": "Reasons", "ticket": "Tickets", "warehouse": "Warehouse", "coupon": "Coupon", "promotion": "Promotion", "offer": "Offers & Deals", "popup": "PopUp", "fulfillment": "Fulfillment"}, "validation": {"required": "This field is required", "email": "Please enter a valid email", "passwordLength": "Password must be at least 8 characters", "passwordMatch": "Passwords must match", "emailRequired": "Email Address is required", "passwordRequired": "Password is required"}, "demo": {"list": {"title": "Product List", "search": "Search products...", "category": "Category", "status": "Status", "reset": "Reset", "show": "Show", "perPage": "per page", "showing": "Showing", "to": "to", "of": "of", "results": "results", "previous": "Previous", "next": "Next", "emptyMessage": "No products found matching your criteria.", "columns": {"id": "ID", "name": "Product Name", "category": "Category", "price": "Price", "rating": "Rating", "status": "Status"}, "statuses": {"inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "discontinued": "Discontinued"}, "categories": {"electronics": "Electronics", "clothing": "Clothing", "homeKitchen": "Home & Kitchen", "books": "Books", "toys": "Toys"}}, "form": {"title": "Form Components", "formData": "Form Data", "submittedData": "Submitted Form Data:", "noDataMessage": "No form data submitted yet. Fill out and submit the form to see the data here.", "cancel": "Cancel", "submit": "Submit", "submitting": "Submitting...", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "emailHelper": "We'll never share your email with anyone else.", "password": "Password", "passwordHelper": "Your password must be at least 8 characters long.", "bio": "Bio", "bioHelper": "Maximum 200 characters", "bioPlaceholder": "Tell us about yourself...", "birthDate": "Birth Date", "birthDateHelper": "Optional: Select your date of birth", "gender": "Gender", "country": "Country", "countryPlaceholder": "Select your country", "employmentStatus": "Employment Status", "notifications": "Receive Notifications", "notificationsHelper": "We'll send you updates about our services.", "terms": "I agree to the terms and conditions", "richText": "Rich Content", "richTextPlaceholder": "Write your rich formatted content here"}, "genders": {"male": "Male", "female": "Female", "other": "Other", "preferNotToSay": "Prefer not to say"}, "employment": {"fullTime": "Full-time", "partTime": "Part-time", "selfEmployed": "Self-employed", "freelance": "Freelance", "unemployed": "Unemployed", "student": "Student", "retired": "Retired"}, "countries": {"us": "United States", "ca": "Canada", "uk": "United Kingdom", "au": "Australia", "de": "Germany", "fr": "France", "jp": "Japan", "in": "India", "br": "Brazil", "ae": "United Arab Emirates"}}}, "category": {"title": "Product Category List", "titleSupport": "Support Category List", "add": "Add Category", "searchPlaceholder": "Search Category list...", "reset": "Reset", "emptyMessage": "No Category found with your search or filters.", "editAction": "Edit Category", "deleteAction": "Delete Category", "columns": {"id": "ID", "name_en": "Name (EN)", "name_ar": "Name (AR)", "code": "Code", "type": "Type", "slug": "Slug", "status": "Status", "actions": "Actions"}}, "attributes": {"title": "Product Attribute List", "add": "Add Attribute", "searchPlaceholder": "Search Attribute list...", "reset": "Reset", "emptyMessage": "No Product Attribute found with your search or filters.", "editAction": "Edit Attribute", "deleteAction": "Delete Attribute", "valuesTitle": "Values Information", "addValue": "Add Value", "attributes": "Attribute"}, "vendor": {"title": "Vendor EOI List", "searchPlaceholder": "Search EOI list...", "emptyMessage": "No Vendor EOI found with your search or filters.", "vendorId": "Vendor ID", "vendorInfo": "Vendor Information", "vendorDirectorInfo": "Vendor Director Information", "vendorSpocInfo": "Vendor Contact Person Information", "vendorSkuInfo": "Stock Keeping Unit Information", "vendorTLInfo": "Trade License Information", "vendorTRInfo": "Tax Registration Information", "vendorSocialInfo": "Social Media Information", "vendorAdditionalInfo": "Vendor Additional Information", "vendorConfirmApproval": "Confirm <PERSON><PERSON><PERSON>", "vendorEOIApproved": "Are you sure you want to approve this EOI?", "businessDetails": "Business Details", "additionalDetails": "Additional Details", "tradeLicenseDetails": "Trade License Details", "tl_license_issuing_authority": "License Issuing Authority", "tl_license_first_issue_date": "License First Issue Date", "tl_license_renewal_date": "License Renewal Date", "tl_license_valid_till": "License Valid Till", "tl_entity_type": "Entity Type", "tl_no_of_partners": "No. of Partners", "tl_doc_copy_of_trade_license": "Copy of Trade License", "vatDetails": "VAT Details", "tax_registration_number": "Tax Registration Number", "trn_issue_date": "TRN Issue Date", "trn_name_in_english": "TRN Name (English)", "trn_name_in_arabic": "TRN Name (Arabic)", "vat_doc_copy_of_registration_certificate": "Copy of VAT Certificate", "directorDetails": "Director's Details", "director_name": "Director's Name", "director_designation": "Director's Designation", "director_full_name_passport": "Full Name (as per passport)", "director_passport_number": "Passport Number", "director_emirates_id_number": "Emirates ID Number", "director_emirates_id_issue_date": "Emirates ID Issue Date", "director_emirates_id_expiry_date": "Emirates ID Expiry Date", "director_email": "Director's <PERSON><PERSON>", "director_mobile": "Director's Mobile", "director_preferred_language": "Preferred Language", "director_passport_copy": "Passport Copy", "director_emirates_id_copy": "Emirates ID Copy", "spoc_passport_number": "SPOC Passport Number", "spoc_emirates_id_number": "SPOC Emirates ID Number", "spoc_emirates_id_issue_date": "SPOC Emirates ID Issue Date", "spoc_emirates_id_expiry_date": "SPOC Emirates ID Expiry Date", "spoc_letter_of_authorization": "SPOC Letter of Authorization", "spoc_passport_copy": "SPOC Passport Copy", "spoc_emirates_id_copy": "SPOC Emirates ID Copy", "spoc_loa_copy": "SPOC LOA Copy", "selfDeclaration": "Self Declaration", "signing_self_declaration": "Signing Self Declaration", "contact": "Contact", "type": "Type", "full_name": "Full Name", "designation": "Designation", "email": "Email", "mobile_number": "Mobile Number", "location": "Location", "address": "Address", "bankDetails": "Bank Details", "bank_name": "Bank Name", "branch_name": "Branch Name", "account_holder_name": "Account Holder Name", "iban_number": "IBAN Number", "original_cheque_number": "Original Cheque Number", "bank_certificate_copy": "Bank Certificate Copy", "manufacturerBrands": "Manufacturer Brand", "businessType": "Business Type", "categoriesToSell": "Categories to Sell"}, "banner": {"title": "Banner List", "add": "Add Banner", "searchPlaceholder": "Search Banner list...", "emptyMessage": "No Banner found with your search or filters.", "editAction": "Edit Banner", "deleteAction": "Delete Banner", "itemsAction": "Banner Items", "banner": "Banner"}, "bannerItems": {"title": "Banner Items List", "add": "Add Banner Items", "searchPlaceholder": "Search Banner Items...", "emptyMessage": "No Banner Items found with your search or filters.", "editAction": "Edit Banner Items", "deleteAction": "Delete Banner Items", "bannerItem": "Banner Items"}, "warehouse": {"title": "Warehouse List", "add": "Add Warehouse", "searchPlaceholder": "Search Warehouse...", "emptyMessage": "No Warehouse found with your search or filters.", "editAction": "Edit Warehouse", "deleteAction": "Delete Warehouse", "warehouse": "Warehouse"}, "productClass": {"title": "Class List", "add": "Add Class", "searchPlaceholder": "Search Class...", "emptyMessage": "No Class found with your search or filters.", "editAction": "Edit Class", "deleteAction": "Delete Class", "class": "Class"}, "blogCategory": {"title": "Blog Category List", "add": "Add Blog Category", "searchPlaceholder": "Search Blog Category...", "emptyMessage": "No Blog Category found with your search or filters.", "editAction": "Edit Blog Category", "deleteAction": "Delete Blog Category", "blogCategory": "Blog Category"}, "blog": {"title": "Blog List", "add": "Add Blog", "searchPlaceholder": "Search Blog...", "emptyMessage": "No Blog found with your search or filters.", "editAction": "Edit Blog", "deleteAction": "Delete Blog", "blog": "Blog", "previewBlogContent": "Preview Blog Content"}, "coupon": {"title": "Coupon List", "add": "Add Coupon", "searchPlaceholder": "Search Coupon...", "emptyMessage": "No Coupon found with your search or filters.", "editAction": "Edit Coupon", "deleteAction": "Delete Coupon", "coupon": "Coupon"}, "dropdownMenu": {"title": "DropDown List", "add": "Add DropDown", "searchPlaceholder": "Search DropDown list...", "emptyMessage": "No DropDown found with your search or filters.", "editAction": "Edit DropDown", "deleteAction": "Delete DropDown", "dropdown": "DropDown"}, "supportTopic": {"title": "Support Topic List", "add": "Add Support Topic", "searchPlaceholder": "Search Support Topic list...", "emptyMessage": "No Support Topic found with your search or filters.", "editAction": "Edit Support Topic", "deleteAction": "Delete Support Topic", "supportTopic": "Support Topic"}, "vendorSection": {"title": "Vendor List", "searchPlaceholder": "Search Vendor list...", "emptyMessage": "No Vendor found with your search or filters."}, "supportTicket": {"title": "Support Ticket List", "add": "Add Support Ticket", "searchPlaceholder": "Search Support Ticket list...", "emptyMessage": "No Support Ticket found with your search or filters.", "editAction": "Edit Support Ticket", "deleteAction": "Delete Support Ticket", "supportTicket": "Support Ticket", "view": "View Support Ticket", "supportCategory": "Category", "supportTopic": "Topic", "supportPriority": "Priority", "supportTicketDetails": "Ticket Details", "supportSubject": "Subject", "supportMessage": "Message", "supportCreated": "Created", "supportReplySummary": "Reply Summary", "supportTicketOverview": "Ticket Overview", "supportVendor": "<PERSON><PERSON><PERSON>", "supportOrder": "Order", "supportQuickActions": "Quick Actions", "supportTPL": "TPL"}, "offerDeal": {"title": "Offer & Deal List", "add": "Add Offer & Deal", "searchPlaceholder": "Search Offer & Deal list...", "emptyMessage": "No Offer & Deal found with your search or filters.", "editAction": "Edit Offer & Deal", "deleteAction": "Delete Offer & Deal", "offerDeal": "Offer & Deal"}, "page": {"title": "Page List", "add": "Add Page", "searchPlaceholder": "Search Page...", "emptyMessage": "No Page found with your search or filters.", "editAction": "Edit Page", "deleteAction": "Delete Page", "page": "page", "pageView": "Page Preview"}, "brand": {"title": "Brand List", "add": "Add Brand", "searchPlaceholder": "Search Brand...", "emptyMessage": "No Brand found with your search or filters.", "editAction": "Edit Brand", "deleteAction": "Delete Brand", "brand": "Brand", "businessType": "Business Type", "skuInformation": "SKU Information", "salesChannels": "Sales Channels", "registrationUSP": "Registration & USP"}, "role": {"title": "Role List", "add": "Add Role", "searchPlaceholder": "Search Role List...", "emptyMessage": "No Role found with your search or filters.", "editAction": "Edit Role", "deleteAction": "Delete Role", "role": "Role", "rolePermissions": "Role Permissions"}, "permission": {"title": "Permission List", "add": "Add Permission", "searchPlaceholder": "Search Permission list...", "emptyMessage": "No Permission found with your search or filters.", "editAction": "Edit Permission", "deleteAction": "Delete Permission", "permission": "Permission"}, "user": {"title": "User List", "add": "Add User", "searchPlaceholder": "Search User list...", "emptyMessage": "No User found with your search or filters.", "editAction": "Edit User", "deleteAction": "Delete User", "user": "User", "userPermissions": "User Permissions", "customizePermission": "Customize this user's permissions beyond their role defaults.", "default": "<PERSON><PERSON><PERSON>", "permissionRole": "Permissions are included in the user's role."}, "customer": {"title": "Customer List", "add": "Add Customer", "searchPlaceholder": "Search Customer list...", "emptyMessage": "No Customer found with your search or filters.", "editAction": "Edit Customer", "deleteAction": "Delete Customer", "customer": "Customer", "knowYourCustomer": "KYC Verification"}, "order": {"title": "Order List", "add": "Add Order", "searchPlaceholder": "Search Order...", "emptyMessage": "No Order found with your search or filters.", "editAction": "Edit Order", "deleteAction": "Delete Order", "order": "Order", "orderItem": "Items", "orderQty": "Qty", "track": "Track", "totalOrders": "Total Orders", "totalRevenue": "Total Revenue", "pendingOrders": "Pending Orders", "deliveredOrders": "Delivered Orders"}, "popup": {"title": "PopUp List", "add": "Add PopUp", "searchPlaceholder": "Search PopUp list...", "emptyMessage": "No PopUp found with your search or filters.", "editAction": "Edit PopUp", "deleteAction": "Delete PopUp", "popup": "PopUp", "previewBlogContent": "Preview PopUp Content"}, "fulfillment": {"title": "Fulfillment List", "add": "Add Fulfillment", "searchPlaceholder": "Search Fulfillment list...", "emptyMessage": "No Fulfillment found with your search or filters.", "editAction": "Edit Fulfillment", "deleteAction": "Delete Fulfillment", "fulfillment": "Fulfillment", "previewFulfillmentContent": "Preview Fulfillment Content"}, "commonPagination": {"showing": "Showing", "to": "to", "of": "of", "results": "results", "show": "Show", "perPage": "Per Page"}, "commonDelete": {"deleteTitle": "Confirm Delete", "deleteMessage": "Are you sure you want to delete the", "deleteWarning": " This action cannot be undone."}, "commonOptions": {"categoryType": {"main": "Main Category", "sub": "Sub Category"}, "status": {"active": "Active", "inactive": "Inactive"}, "yesNo": {"Yes": "Yes", "No": "No"}, "required": "Required", "optional": "Optional", "progressStatusOptions": {"open": "Open", "progress": "Progress", "resolved": "Resolved", "closed": "Closed"}}, "common": {"cancel": "Cancel", "create": "Create", "save": "Save", "saving": "Saving...", "loading": "Loading...", "update": "Update", "edit": "Edit", "delete": "Delete", "retry": "Retry", "back": "Back", "close": "Close", "actions": "Actions"}, "commonButton": {"add": "Add", "cancel": "Cancel", "delete": "Delete", "remove": "Remove", "reset": "Reset", "loading": "Loading", "filter": "Filters", "activeFilters": "Active Filters", "notAvailable": "N/A", "approval": "Approve", "resetToRoleDefaults": "Reset to <PERSON>s", "category": {"create": "Create Category", "updated": "Update Category"}, "attribute": {"create": "Create Attribute", "updated": "Update Attribute"}, "dropDown": {"create": "Create DropDown", "updated": "Update DropDown"}, "banner": {"create": "Create Banner", "updated": "Update Banner"}, "bannerItem": {"create": "Create Banner Items", "updated": "Update Banner Items"}, "warehouse": {"create": "Create Warehouse", "updated": "Update Warehouse"}, "coupon": {"create": "Create Coupon", "updated": "Update Coupon"}, "class": {"create": "Create Class", "updated": "Update Class"}, "blogCategory": {"create": "Create Blog Category", "updated": "Update Blog Category"}, "blog": {"create": "Create Blog", "updated": "Update Blog"}, "supportTicket": {"create": "Create Ticket", "updated": "Update Ticket", "saveChanges": "Save Changes", "addMessage": "Add Message"}, "offerDeal": {"create": "Create Offer & Deal", "updated": "Update Offer & Deal"}, "page": {"create": "Create Page", "updated": "Update Page"}, "brand": {"create": "Create Brand", "updated": "Update Brand"}, "role": {"create": "Create Role", "updated": "Update Role"}, "permission": {"create": "Create Permission", "updated": "Update Permission"}, "user": {"create": "Create User", "updated": "Update User"}, "customer": {"create": "Create Customer", "updated": "Update Customer"}, "popup": {"create": "Create PopUp", "updated": "Update PopUp"}, "fulfillment": {"create": "Create Fulfillment", "updated": "Update Fulfillment"}}, "commonTableLabel": {"id": "ID", "name_en": "Name (EN)", "name_ar": "Name (AR)", "vendor_name_en": "Vendor Name (EN)", "vendor_name_ar": "Vendor Name (AR)", "approval_status": "Approval Status", "status": "Status", "actions": "Actions", "previewAction": "Preview page", "categoryName": "Category Name", "description": "Description", "templatesCount": "Templates Count", "businessType": "Business Type", "website": "Website", "passportName": "Name (As on Passport)", "publicName": "Public Name", "designation": "Designation", "mobile": "Mobile Number", "email": "Email", "preferredLanguage": "Preferred Language", "passportNumber": "Passport Number", "eID": "National ID", "eIDIssueDate": "National ID Issue Date", "eIDExpiryDate": "National ID Expiry Date", "onAmazon": "Amazon", "onNoon": "<PERSON>on", "onOtherMarketplaces": "Other Marketplaces", "onOwnWebsite": "Own Website", "letterAuthorization": "SPOC Letter of Authorization", "tlType": "Entity Type", "issuingAuthority": "Issuing Authority", "issuingDate": "Issuing Date", "expiryDate": "Expiry Date", "taxRegNumber": "Tax Registration Number", "facebook": "facebook", "instagram": "Instagram", "otherSocialMedia": "Other Social Media", "brandSell": "Brands to Sell", "categoriesToSell": "Categories to Sell", "distributorStores": "Distributor Stores", "importerBrands": "Imported Brands", "manufacturerBrands": "Manufactured Brands", "retailerTotalOutlets": "Total Retail Outlets", "additionalInfo": "Additional Info", "key": "Key", "value_en": "Value (EN)", "value_ar": "Value (AR)", "sort_order": "Sort Order", "inventoryManagement": "Inventory Management", "orderCollectionLocation": "Order Collection Location", "orderCollectionLocationDetails": "Order Collection Location Details", "title": "Title", "code": "Code", "type": "Type", "slug": "Slug", "image": "Image", "title_en": "Title (EN)", "title_ar": "Title (AR)", "alt_text": "Alt Text", "contactPerson": "contact Person", "contactNumber": "contact Number", "category": "Category", "subCategory": "Sub-Category", "popular": "Popular", "blogCategory": "Blog Category", "metaTitle": "Meta Title", "subject": "Subject", "priority": "Priority", "topic": "Topic", "assignTo": "Assign", "country": "Country", "name": "Name", "guardName": "Guard Name", "phone": "Phone", "verified": "Verified", "profilePicture": "Profile Picture", "password": "Password", "confirmPassword": "Confirm Password", "spocName": "SPOC Name", "spocEmail": "SPOC Email", "spocPhone": "SPOC Phone", "appliedOn": "Applied On", "eoiId": "EOI ID", "templateName": "Template Name", "language": "Language", "createdAt": "Created At", "variableKey": "Variable Key", "variableName": "Variable Name", "dataType": "Data Type", "required": "Required", "sortOrder": "Sort Order", "order": "Order", "customer": "Customer", "vendor": "<PERSON><PERSON><PERSON>", "time": "Time", "amount": "Amount", "paymentStatus": "Payment Status", "orderState": "Order Status", "paymentMethod": "Payment Type", "dateRange": "Date Range", "orderValue": "Order Value", "ban": "Ban Status"}, "commonField": {"name_en": "Name (In English)", "name_ar": "Name (In Arabic)", "code": "Code", "fee_text": "Fee", "slug": "Slug", "meta_options": "Meta Options", "meta_title_en": "Meta Title (In English)", "meta_description": "Meta Description", "category_type": "Category Type", "selectMainCategory": "Select Main Category", "status": "Status", "ordering_serial": "Ordering Serial", "banner": "Upload Banner", "cover_image": "Upload Cover", "icon": "Upload Icon", "value_en": "Value (In English)", "value_ar": "Value (In Arabic)", "approval": "Approved", "title": "Title", "description": "Description", "title_en": "Title (In English)", "title_ar": "Title (In Arabic)", "link": "Link", "position": "Position", "alt_text": "Alt Text", "itemsImage": "Items Image", "emailAddress": "Email Address", "isGlobal": "Is Global", "address": "Address", "location": "Location", "discountValue": "Discount Value", "discountType": "Discount Type", "minOrderValue": "Min Order Value", "usageLimit": "Usage Limit", "perUserLimit": "Per User Limit", "description_en": "Description (In English)", "description_ar": "Description (In Arabic)", "start_date": "Start Date", "end_date": "End Date", "category": "Category", "subCategory": "Sub-Category", "assignMainClass": "Assign to Main Class ?", "mainClass": "Main Class", "isPopular": "Is Popular", "bannerImage": "Banner Image", "coverImage": "Cover Image", "content_en": "Content (In English)", "content_ar": "Content (In Arabic)", "summary_en": "Summary (In English)", "summary_ar": "Summary (In Arabic)", "title_Meta": "Title In Meta", "keyword": "Keywords", "featuredImage": "Featured Image", "topicType": "Topic", "priority": "Priority", "vendor": "<PERSON><PERSON><PERSON> (Optional)", "subject": "Subject", "message": "Message", "order": "Order (Optional)", "assignTo": "Assign To", "tag": "Tag", "regularPrice": "Regular Price", "discountPrice": "Discount Price", "offerPrice": "Offer Price", "countryOrigin": "Country of Origin", "website": "Website Link", "instagramPage": "Instagram Page Link", "facebookPage": "facebook Page Link", "manufacturer": "Manufacturer", "brandOwner": "Brand Owner", "marketingAuthHolder": "Marketing Auth Holder", "exclusiveDistributor": "Exclusive Distributor", "authorizedDistributor": "Authorized Distributor", "wholesaler": "Wholesaler", "authorizedRetailer": "Authorized Retailer", "directImporter": "Direct Importer", "parallelImporter": "Parallel Importer", "dropShipper": "Drop Shipper", "skuBrandWebsite": "SKUs on Brand Website", "skuAmazon": "SKUs on Amazon", "skuNoon": "SKUs on Noon", "skuOtherMarketplaces": "SKUs on Other Marketplaces", "skuOwnWebsite": "SKUs on Own Website", "soldHypermarkets": "Sold in Hypermarkets", "soldPharmacies": "Sold in Pharmacies", "soldSpecialtyStores": "Sold in Specialty Stores", "MOHAPRegistration": "MOHAP Registration", "dubaiMunicipalityRegistration": "Dubai Municipality Registration", "brandUSP": "Brand USP", "topProducts": "Top Products (comma-separated)", "brandLogo": "Brand Logo", "trademarkDocument": "Trademark Document", "relationshipProof": "Relationship Proof", "purchaseProof": "Purchase Proof", "selfDeclaration": "Self Declaration", "productPictures": "Product Pictures", "isTrademarkRegistered": "Is Trademark Registered ?", "document": "Document", "approvalState": "Approval State", "approvalStatus": "Approval Status", "verify": "Verify", "avatar": "Avatar", "birthDate": "Birth Date", "gender": "Gender", "companyName": "Company Name", "kycDocument": "KYC Document", "kycVerify": "KYC Verify", "kycFile": "Upload KYC Proof", "occupation": "Occupation", "customerType": "Customer Type", "preferredLanguage": "Preferred Language", "preferredCurrency": "Preferred Currency", "loyaltyPoints": "Loyalty Points", "referredBy": "Referred By", "referralCode": "Referral Code", "vrps": "Valuable Regular & Premium Shoppers", "newsletterConsent": "Subscribe to Newsletter", "loyaltyPointsAwarded": "Loyalty Points Awarded", "uploadImage": "Upload Image", "buttonText": "Button Text", "buttonLink": "Button Link"}, "commonPlaceholder": {"name_enPlaceholder": "Enter the name in English", "name_arPlaceholder": "Enter the name in Arabic", "codePlaceholder": "Enter the code", "fee_textPlaceholder": "Enter the fee", "slugPlaceholder": "Enter the slug", "meta_titlePlaceholder": "Enter the meta title in English", "meta_descriptionPlaceholder": "Enter the meta description", "ordering_serialPlaceholder": "Enter the ordering serial", "value_enPlaceholder": "Enter the value in English", "value_arPlaceholder": "Enter the value in Arabic", "titlePlaceholder": "Enter the Title Name", "descriptionPlaceholder": "Enter the Description", "title_enPlaceholder": "Enter the Title in English", "title_arPlaceholder": "Enter the Title in Arabic", "linkUrlPlaceholder": "Enter the link url", "positionPlaceholder": "Enter the Title in position", "altTextPlaceholder": "Enter the Alt Text", "emailPlaceholder": "Enter the Email Address", "ContactPersonPlaceholder": "Enter The Contact Person Name", "ContactPersonNumberPlaceholder": "Enter The Contact Person Number", "addressPlaceholder": "Enter The Address", "locationPlaceholder": "Enter The Location", "discountValuesPlaceholder": "Enter The Discount values", "minOrderValuePlaceholder": "Enter The Min Order Values", "usageLimitPlaceholder": "Enter The Usage Limit", "perUserLimitPlaceholder": "Enter The Per User Limit", "description_enPlaceholder": "Enter The Description in English", "description_arPlaceholder": "Enter The Description in Arabic", "content_enPlaceholder": "Enter Content in English", "content_arPlaceholder": "Enter Content in Arabic", "summary_enPlaceholder": "Enter Summary in English", "summary_arPlaceholder": "Enter Summary in Arabic", "keywordPlaceholder": "Enter keywords", "vendorPlaceholder": "Enter The Vendor ID", "orderPlaceholder": "Enter The Order ID", "subjectPlaceholder": "Enter The Subject", "messagePlaceholder": "Enter The Message", "tagPlaceholder": "Enter The Tag", "regularPricePlaceholder": "Enter The Regular Price", "discountPricePlaceholder": "Enter The Discount Price", "offerPricePlaceholder": "Enter The Offer Price", "countryOriginPlaceholder": "Enter country of origin", "websitePlaceholder": "Enter the Website URL (e.g., https://example.com)", "instagramPlaceholder": "Enter the Instagram URL (e.g., https://instagram.com)", "facebookPlaceholder": "Enter the Facebook URL (e.g., https://facebook.com)", "soldHypermarketPlaceholder": "Enter The Sold in Hypermarkets", "soldPharmaciesPlaceholder": "Enter The Sold in Pharmacies", "soldSpecialtyStoresPlaceholder": "Enter The Sold in Specialty Stores", "brandUniqueSellingPropositionPlaceholder": "Enter brand's unique selling proposition", "egProductOneProductTwoProductThree": "e.g., Product 1, Product 2, Product 3", "namePlaceholder": "Enter the Name", "referredByCodePlaceholder": "Enter The Referred By Code", "referredByPlaceholder": "Enter The Referred By", "passwordPlaceholder": "Enter The Password", "confirmationPasswordPlaceholder": "Enter The Confirmation Password", "loyaltyPointsPlaceholder": "Enter The Loyalty Number", "filePlaceholder": "Select your document", "buttonTextPlaceholder": "Enter the Button Name", "buttonLinkPlaceholder": "Enter the Button Link"}, "commonValidation": {"name_en": "Enter the Name is required", "category_type": "Category Type is required", "slug": "Slug is required", "status": "Status is required", "value_en": "Value (EN) is required", "atLeastOneValue": "At least one value is required", "valuesRequired": "Values are required", "title": "Title is required", "title_en": "Title (English) is required", "title_ar": "Title (Arabic) is required", "code": "Code is required", "discountType": "Discount type is required", "discountValue": "Discount value is required", "discountValueError": "Discount value must be a number", "minOrderValueError": "Minimum order value must be a number", "usageLimitError": "Usage limit must be a number", "perUserLimitError": "Per user limit must be a number", "blogCategory": "Blog Category is required", "perUserLimitExceeded": "Per user limit cannot exceed the total usage limit.", "startDate": "Start Date is required.", "endDate": "End Date is required.", "endDateBeforeStart": "End Date cannot be before start date.", "subject": "Subject is required", "message": "Message is required", "priority": "Priority is required", "topic": "Topic is required", "type": "Type is required", "validNumber": "Enter the number", "positiveNumber": "The number must be positive", "brand": "Brand name is required", "mustBeURL": "Must be a valid URL", "skuBrandWebsite": "Skus on Brand Website must be a number", "skuAmazon": "Skus on Amazon must be a number", "skuNoon": "Skus on <PERSON>on must be a number", "notNegativeNumber": "The number cannot be negative", "skuOtherMarketplaces": "Skus on Other Marketplaces must be a number", "skuOwnWebsite": "Skus on Own Website must be a number", "name": "Name is required", "leastOnePermission": "Select at least one permission", "email": "Email Address is required", "invalidEmail": "Invalid email address", "role": "Role is required", "password": "Password is required", "passwordLength": "Password must be at least 8 characters", "confirmPassword": "Confirm password is required", "passwordMatch": "Password must be match", "imageRequired": "Image is required", "phone": "Phone Number is required", "phoneInvalid": "Please Enter numbers only", "emailInvalid": "Please enter a valid email address", "loyaltyPointsInvalid": "Loyalty points must contain only numbers"}, "uploadImage": {"uploadTitle": "Drop your image here, or", "uploadLink": "Browse"}, "commonToast": {"categoryToast": {"categoryDelete": "Category Deleted successfully", "categoryCreate": "Category Created successfully", "categoryUpdate": "Category Updated successfully"}, "attributeToast": {"attributeDelete": "Attribute Deleted successfully", "attributeCreate": "Attribute Created successfully", "attributeUpdate": "Attribute Updated successfully"}, "dropDownToast": {"dropDownDelete": "DropDown Deleted successfully", "dropDownCreate": "DropDown Created successfully", "dropDownUpdate": "DropDown Updated successfully"}, "bannerToast": {"bannerDelete": "Banner Deleted successfully", "bannerCreate": "Banner Created successfully", "bannerUpdate": "Banner Updated successfully"}, "bannerItemsToast": {"bannerItemDelete": "Banner items Deleted successfully", "bannerItemCreate": "Banner items Created successfully", "bannerItemUpdate": "Banner items Updated successfully"}, "warehouseToast": {"warehouseDelete": "Warehouse Deleted successfully", "warehouseCreate": "Warehouse Created successfully", "warehouseUpdate": "Warehouse Updated successfully"}, "couponToast": {"couponDelete": "Coupon Deleted successfully", "couponCreate": "Coupon Created successfully", "couponUpdate": "Coupon Updated successfully"}, "classToast": {"classDelete": "Class Deleted successfully", "classCreate": "Class Created successfully", "classUpdate": "Class Updated successfully"}, "blogCategoryToast": {"blogCategoryDelete": "Blog Category Deleted successfully", "blogCategoryCreate": "Blog Category Created successfully", "blogCategoryUpdate": "Blog Category Updated successfully"}, "blogToast": {"blogDelete": "Blog Deleted successfully", "blogCreate": "Blog Created successfully", "blogUpdate": "Blog Updated successfully"}, "supportTopicToast": {"supportTopicDelete": "Support Topic Deleted successfully", "supportTopicCreate": "Support Topic Created successfully", "supportTopicUpdate": "Support Topic Updated successfully"}, "supportTicketToast": {"supportTicketDelete": "Support Ticket Deleted successfully", "supportTicketCreate": "Support Ticket Created successfully", "supportTicketUpdate": "Support Ticket Updated successfully"}, "offerDealToast": {"offerDealDelete": "Offer & Deal Deleted successfully", "offerDealCreate": "Offer & Deal Created successfully", "offerDealUpdate": "Offer & Deal Updated successfully"}, "pageToast": {"pageDelete": "Page Deleted successfully", "pageCreate": "Page Created successfully", "pageUpdate": "Page Updated successfully"}, "brandToast": {"brandDelete": "Brand Deleted successfully", "brandCreate": "Brand Created successfully", "brandUpdate": "Brand Updated successfully"}, "roleToast": {"roleDelete": "Role Deleted successfully", "roleCreate": "Role Created successfully", "roleUpdate": "Role Updated successfully"}, "permissionToast": {"permissionDelete": "Permission Deleted successfully", "permissionCreate": "Permission Created successfully", "permissionUpdate": "Permission Updated successfully"}, "userToast": {"userDelete": "User Deleted successfully", "userCreate": "User Created successfully", "userUpdate": "User Updated successfully"}, "customerToast": {"customerDelete": "Customer Deleted successfully", "customerCreate": "Customer Created successfully", "customerUpdate": "Customer Updated successfully"}, "popupToast": {"popupDelete": "PopUp Deleted successfully", "popupCreate": "PopUp Created successfully", "popupUpdate": "PopUp Updated successfully"}, "fulfillmentToast": {"fulfillmentDelete": "Fulfillment Deleted successfully", "fulfillmentCreate": "Fulfillment Created successfully", "fulfillmentUpdate": "Fulfillment Updated successfully"}, "banToast": {"banUpdate": "Customer ban status updated successfully"}}, "products": {"emptyMessage": "No products found matching your criteria.", "searchPlaceholder": "Search products by name, SKU, or description...", "addProduct": "Add Product", "editProduct": "Edit Product", "createProduct": "Create New Product", "productManagement": "Product Management", "allProducts": "All Products", "productDetails": "Product Details", "basicInformation": "Basic Information", "descriptionContent": "Description & Content", "mediaManagement": "Media Management", "pricingInventory": "Pricing & Inventory", "productVariants": "Product Variants", "fulfillmentLogistics": "Fulfillment & Logistics", "complianceCertifications": "Compliance & Certifications", "seoOptimization": "SEO Optimization", "faqsManagement": "FAQs Management", "reviewSubmit": "Review & Submit", "saveDraft": "Save Draft", "submitForApproval": "Submit for Approval", "overallProgress": "Overall Progress", "stepsCompleted": "steps completed", "complete": "Complete", "autoSaving": "Auto-saving...", "layoutToggle": "Layout Toggle", "horizontalLayout": "Horizontal Layout", "verticalLayout": "Vertical Layout", "step": "Step", "of": "of", "completed": "completed", "previous": "Previous", "next": "Next", "cancel": "Cancel", "filters": {"allStatus": "All Status", "active": "Active", "draft": "Draft", "inactive": "Inactive", "allCategories": "All Categories", "allBrands": "All Brands", "allPrices": "All Prices", "allStock": "All Stock", "inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "priceRange": "Price Range", "stockStatus": "Stock Status", "clear": "Clear", "activeFilters": "Active filters"}, "table": {"image": "Image", "productName": "Product Name", "sku": "SKU", "category": "Category", "price": "Price", "stock": "Stock", "status": "Status", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "offer": "Offer", "primary": "Primary"}, "messages": {"deleteConfirm": "Are you sure you want to delete this product?", "deleteSuccess": "Product deleted successfully", "deleteError": "Failed to delete product", "saveSuccess": "Product saved successfully", "saveError": "Failed to save product", "submitSuccess": "Product submitted for approval successfully", "submitError": "Failed to submit product", "incompleteStep": "Please fill in all required fields before proceeding", "incompleteProduct": "Please complete at least 80% of the form before submitting", "autoSaveError": "Auto-save failed"}, "completeStepsDescription": "Complete all steps to create and publish your product"}, "emailTemplate": {"title": "Email Templates", "subtitle": "Manage email templates for automated communications", "createNew": "Create Template", "createTitle": "Create <PERSON><PERSON>", "createSubtitle": "Create a new email template for your communications", "editTitle": "Edit <PERSON><PERSON>late", "editSubtitle": "Update the email template", "duplicateTitle": "Duplicate Email Template", "duplicateSubtitle": "Create a copy of the existing template", "name": "Template Name", "namePlaceholder": "Enter template name", "subject": "Email Subject", "subjectPlaceholder": "Enter email subject", "selectCategory": "Select a category", "category": "Category", "language": "Language", "status": "Status", "setAsDefault": "Set as <PERSON><PERSON><PERSON>", "defaultHelp": "Default templates are used when no specific template is selected", "content": "Content", "htmlContent": "HTML Content", "htmlPlaceholder": "Enter HTML content for the email", "textContent": "Text Content", "textPlaceholder": "Enter plain text version (optional)", "textHelp": "Plain text version for email clients that don't support HTML", "variables": "Variables", "availableVariables": "Available Variables", "selectedVariables": "Selected Variables", "noVariablesSelected": "No variables selected", "noVariables": "No variables available", "variableCopied": "Variable copied to clipboard", "variableAdded": "Variable added to template", "clickToCopy": "Click to copy variable", "clickToAdd": "Click to add variable", "variableSelected": "Variable selected", "removeVariable": "Remove variable", "variableUsage": "Variable Usage", "variableUsageDescription": "Use double curly braces to insert variables: {{variable.name}}", "examples": "Examples", "exampleUserName": "User's full name", "exampleSiteName": "Website name", "exampleOrderNumber": "Order number", "conditionalLogic": "Conditional Logic", "loops": "Loops", "variableLabels": {"user.name": "User Name", "site.name": "Site Name", "site.url": "Site URL"}, "basicInformation": "Basic Information", "helpTitle": "Template Help", "templates": "templates", "noTemplates": "No templates found", "searchPlaceholder": "Search templates...", "allLanguages": "All Languages", "allStatuses": "All Statuses", "allCategories": "All Categories", "categoriesLoadError": "Failed to load categories", "loadError": "Failed to load templates", "deleteSuccess": "Template deleted successfully", "deleteError": "Failed to delete template", "createSuccess": "Template created successfully", "createError": "Failed to create template", "updateSuccess": "Template updated successfully", "updateError": "Failed to update template", "duplicateError": "Failed to duplicate template", "deleteConfirmTitle": "Delete Template", "deleteConfirmMessage": "Are you sure you want to delete the template '{{name}}'?", "default": "<PERSON><PERSON><PERSON>", "manageCategories": "Manage Categories", "manageVariables": "Manage Variables", "editTemplate": "Edit Template", "duplicate": "Duplicate", "create": "Create", "edit": "Edit", "delete": "Delete", "templatesInCategory": "Templates in Category", "noTemplatesInCategory": "No templates in this category", "noTemplatesInCategoryDescription": "This category doesn't have any templates yet.", "createFirst": "Create First Template", "preview": "Preview", "previewTitle": "Template Preview", "htmlSource": "HTML Source", "sendTest": "Send Test", "sendTestEmail": "Send Test Email", "testEmailDescription": "Send a test email to verify the template", "testEmailAddress": "Test Email Address", "testEmailRequired": "Test email address is required", "testEmailSent": "Test email sent successfully", "testEmailError": "Failed to send test email", "emailContent": "Email Content", "noPreviewData": "No preview data available", "previewError": "Failed to generate preview", "validation": {"nameRequired": "Template name is required", "subjectRequired": "Email subject is required", "bodyRequired": "Email content is required", "categoryRequired": "Category is required", "languageRequired": "Language is required"}, "categoryManagement": {"title": "Template Categories", "subtitle": "Organize your email templates into categories", "createNew": "Create Category", "add": "Add", "createTitle": "Create Category", "editTitle": "Edit Category", "edit": "Edit", "delete": "Delete", "name": "Category Name", "namePlaceholder": "Enter category name", "description": "Description", "descriptionPlaceholder": "Enter category description", "icon": "Icon", "iconPlaceholder": "📧", "iconHelp": "Use an emoji or icon character", "sortOrder": "Sort Order", "sortOrderHelp": "Lower numbers appear first", "templatesCount": "Templates", "slug": "Slug", "list": "Categories", "noCategories": "No categories found", "searchPlaceholder": "Search categories...", "preview": "Preview", "loadError": "Failed to load categories", "createSuccess": "Category created successfully", "createError": "Failed to create category", "updateSuccess": "Category updated successfully", "updateError": "Failed to update category", "deleteSuccess": "Category deleted successfully", "deleteError": "Failed to delete category", "deleteConfirmTitle": "Delete Category", "deleteConfirmMessage": "Are you sure you want to delete the category '{{name}}'?", "deleteWarning": "This category has {{count}} templates. Please move or delete them first.", "validation": {"nameRequired": "Category name is required", "nameMin": "Category name must be at least 2 characters", "nameMax": "Category name cannot exceed 100 characters", "descriptionMax": "Description cannot exceed 500 characters", "iconMax": "Icon cannot exceed 50 characters", "sortOrderRequired": "Sort order is required", "sortOrderMin": "Sort order must be at least 1", "sortOrderMax": "Sort order cannot exceed 999"}}, "variable": {"title": "Template Variables", "subtitle": "Manage dynamic variables for email templates", "createNew": "Create Variable", "add": "Add", "createTitle": "Create Variable", "editTitle": "Edit Variable", "key": "Variable Key", "keyPlaceholder": "user.name", "keyHelp": "Use dot notation for nested variables (e.g., user.name)", "name": "Variable Name", "namePlaceholder": "User Full Name", "description": "Description", "descriptionPlaceholder": "The full name of the user", "categoryLabel": "Category", "dataTypeLabel": "Data Type", "required": "Required", "example": "Example", "exampleValue": "Example Value", "examplePlaceholder": "<PERSON>", "exampleHelp": "Example value for preview and testing", "defaultValue": "Default Value", "defaultPlaceholder": "Default value if empty", "defaultHelp": "Value to use when variable is not provided", "validationRules": "Validation Rules", "maxLength": "Max Length", "maxLengthHelp": "Maximum character length", "pattern": "Pattern", "patternHelp": "Regular expression pattern for validation", "preview": "Preview", "usage": "Usage", "exampleOutput": "Example Output", "list": "Variables", "noVariables": "No variables found", "searchPlaceholder": "Search variables...", "allCategories": "All Categories", "allDataTypes": "All Data Types", "allRequired": "All", "loadError": "Failed to load variables", "createSuccess": "Variable created successfully", "createError": "Failed to create variable", "updateSuccess": "Variable updated successfully", "updateError": "Failed to update variable", "deleteSuccess": "Variable deleted successfully", "deleteError": "Failed to delete variable", "deleteConfirmTitle": "Delete Variable", "deleteConfirmMessage": "Are you sure you want to delete the variable '{{key}}'?", "category": {"user": "User", "order": "Order", "vendor": "<PERSON><PERSON><PERSON>", "site": "Site", "auth": "Authentication"}, "dataType": {"string": "String", "number": "Number", "boolean": "Boolean", "array": "Array", "object": "Object"}, "validation": {"nameRequired": "Variable name is required", "nameMin": "Variable name must be at least 2 characters", "nameMax": "Variable name cannot exceed 100 characters", "keyRequired": "Variable key is required", "keyFormat": "Variable key must use lowercase letters, numbers, dots and underscores only", "keyMax": "Variable key cannot exceed 100 characters", "descriptionMax": "Description cannot exceed 500 characters", "dataTypeRequired": "Data type is required", "categoryRequired": "Category is required", "exampleMax": "Example value cannot exceed 255 characters", "defaultMax": "Default value cannot exceed 255 characters"}}, "history": {"title": "Template History", "versionHistory": "Version History", "currentVersion": "Current Version", "current": "Current", "version": "Version", "lastModified": "Last Modified", "versionDetails": "Version Details", "changedBy": "Changed By", "changeDate": "Change Date", "changeReason": "Change Reason", "noReason": "No reason provided", "noHistory": "No version history", "noHistoryDescription": "This template doesn't have any version history yet.", "variablesAdded": "{{count}} variables added", "variablesRemoved": "{{count}} variables removed", "subjectChanged": "Subject changed", "bodyChanged": "Content changed", "minorChanges": "Minor changes", "noChanges": "No changes", "restoreVersion": "Restore Version", "restore": "Rest<PERSON>", "restoreConfirmMessage": "Are you sure you want to restore to version {{version}}? This will create a new version.", "restoreReason": "Restore Reason", "restoreReasonPlaceholder": "Why are you restoring this version?", "restoreReasonRequired": "Restore reason is required", "restoreSuccess": "Template restored successfully", "restoreError": "Failed to restore template", "loadError": "Failed to load template history", "loadVersionError": "Failed to load version details"}}}